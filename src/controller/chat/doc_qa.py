import re
import time
import asyncio
import traceback
import uuid
from datetime import datetime
from enum import StrEnum
from typing import List, Annotated, Optional, AsyncGenerator, AsyncIterable

from openai import AsyncStream
from openai.types.chat import ChatCompletionChunk
from pydantic import Field, BaseModel

from common.time import now_tz_datestring_with_millis
from common.logger import logger
from controller.operator.runner.qa.generate_title import GenerateTitle
from controller.stats import TokenCounts, LLMBusiness
from engine.rdb import g
from config import MAX_HISTORY_LEN, LLMModel
from model.session import ChatMessage, QAStatus
from controller.chat.buffer import QABuffer, BufferType
from controller.chat.session import Session
from controller.operator.runner.base import RunnerStatus
from controller.operator.chunking import RetrieveChunkModel
from controller.operator.runner.qa.doc_qa import DocQA, BaseRunner
from controller.retriever import HybridRetriever, WebSearchConfig
from controller.system import ModelInfo
from controller.tenant import Strategy
from controller.user import UserRetrieve


class StreamStage(StrEnum):
    THINKING = "thinking"
    GENERATING = "generating"
    CORNER = "corner"
    REFERENCE = "reference"
    ERROR = "error"


class CompletionStreamResponse(BaseModel):
    stage: Annotated[StreamStage, Field(title="思考阶段")]
    session_id: Annotated[int, Field(title="会话ID")]
    request_id: Annotated[str, Field(title="请求ID")]
    create_time: Annotated[datetime, Field(title="创建时间", default_factory=datetime.now)] = None
    content: Annotated[Optional[str], Field(title="回复内容")] = None
    reference: Annotated[Optional[list], Field(title="引用文献")] = None
    error_msg: Annotated[Optional[str], Field(title="错误信息")] = None


class DocQAHelper:
    def __init__(self,
                 session_id: str | int,
                 user: str,
                 history: List[ChatMessage] = None,
                 chat_model: str = LLMModel.DEEPSEEK_REASONER,
                 repo_ids: List[int] = None,
                 doc_ids: List[int] = None,
                 user_retrieve: bool = False,
                 web_search: WebSearchConfig | None = None):
        """
        Parameters:
            session_id: 会话ID
            user: 用户输入的问题
            history: 历史聊天记录
            chat_model: 聊天模型名称
            repo_ids: 仓库ID列表
            doc_ids: 文档ID列表
            user_retrieve: 是否启用用户检索
            web_search: 网络搜索配置
        """
        # 标准传入参数
        self.session_id = session_id
        self.user_prompt = user  # user_prompt可能为修改后的结果
        self.history = history if history else None
        self.chat_model = chat_model

        # 召回传入参数
        self.repo_ids = repo_ids
        self.doc_ids = doc_ids
        self.user_retrieve = user_retrieve
        self.web_search = web_search

        # 召回构建参数
        self.chat_retrieval_strategy: dict | None = None  # indexing阶段补充
        self.chat_model_info: dict | None = None  # indexing阶段补充
        self.chunks: List[RetrieveChunkModel] = []  # retrieve阶段补充

        # 构建参数
        self.user_id = g.user_id
        self.chat_message = ChatMessage(
            request_id=str(uuid.uuid4()),
            user=user,
            query_time=now_tz_datestring_with_millis(),
            repo_ids=self.repo_ids,
            doc_ids=self.doc_ids,
            web_search=bool(self.web_search),
            user_id=g.user_id
        )
        self.config = {
            "web_search": {
                "name": self.web_search.name,
                "search_config": self.web_search.search_config.model_dump()} if self.web_search else None,
            "user_retrieve": self.user_retrieve,
            "repo_ids": self.repo_ids,
            "doc_ids": self.doc_ids
        }
        self.stop_generation = False  # 用于控制生成的停止
        self.runner: Optional[BaseRunner] = None


    async def generator(self) -> AsyncGenerator:
        await Session.start_streaming(session_id=self.session_id)
        asyncio.create_task(self._check_stop_generation())

        try:
            await self.indexing()
            await self.retrieving()

            # 参考文献 ID 的映射是为了避免复杂的 ID 干扰大模型的效果(prince: 此变换已经修改到内层)
            self.runner = runner = DocQA(
                chunks=self.chunks,
                user=self.user_prompt,
                history=self.history,
                model_name=self.chat_model
            )

            stream: AsyncStream[ChatCompletionChunk] = await runner.run()
            thinking, assistant = "", ""
            index_ref, use_chunk = [], []
            buffer = QABuffer(stream=self._generating(stream=stream), reference=self.chunks)
            self.chat_message.streaming_start_time = now_tz_datestring_with_millis()

            async for buff in buffer.generator():
                if self.stop_generation:
                    break
                if buff.type_ == BufferType.THINK_CONTENT:
                    thinking += buff.content
                    yield self.response_chunk(content=buff.content, stage=StreamStage.THINKING)
                elif buff.type_ == BufferType.CONTENT:
                    assistant += buff.content
                    yield self.response_chunk(content=buff.content, stage=StreamStage.GENERATING)
                elif buff.type_ == BufferType.CORNER:
                    ref_id = int(buff.content)
                    if ref_id < len(self.chunks):  # 添加边界检查
                        chunk = self.chunks[ref_id]
                        # ref_id从原先的chunks索引位置,变为从1开始的递增数字
                        if ref_id not in index_ref:
                            refer = chunk.model_dump()
                            index_ref.append(ref_id)
                            index_ref_id = len(index_ref)
                            refer["ref_id"] = index_ref_id
                            use_chunk.append(refer)
                        else:
                            refer = use_chunk[index_ref.index(ref_id)]
                            index_ref_id = index_ref.index(ref_id) + 1

                        corner_content = f"[ref_id:{index_ref_id}]"
                        assistant += corner_content
                        yield self.response_chunk(content=corner_content, reference=[refer], stage=StreamStage.CORNER)
                    else:
                        logger.warning(f"Invalid ref_id {ref_id}, chunks length: {len(self.chunks)}")

            yield self.response_chunk(reference=use_chunk, stage=StreamStage.REFERENCE)

            self.chat_message.reference = use_chunk
            self.chat_message.assistant = assistant
            if thinking:
                self.chat_message.thinking = thinking

        except Exception as e:
            self.chat_message.error_msg = ''.join(traceback.format_exception(type(e), value=e, tb=e.__traceback__))
            if self.runner and self.runner.status == RunnerStatus.FAILED:
                self.chat_message.qa_status = QAStatus.llm_response_error
            else:
                self.chat_message.qa_status = QAStatus.error
            logger.error(f"Failed to generate completion stream: {self.chat_message.error_msg}")
            yield self.response_chunk(error_msg=self.chat_message.error_msg, stage=StreamStage.ERROR)

        finally:
            await Session.stop_streaming(session_id=self.session_id)
            self.chat_message.streaming_end_time = now_tz_datestring_with_millis()
            logger.info(f"Chat history: {self.chat_message.model_dump_json(indent=4, exclude={'repo_ids', 'filter_docs', 'web_search_docs', 'search_repo_chunks'})}")
            await self.post_processing()

        yield "data: [DONE]\n\n"

    async def indexing(self):
        self.history = await self.get_history()

        if not self.history:
            _ = asyncio.create_task(self._generate_title())
        self.chat_retrieval_strategy = await Strategy.get_chat_retrieval_strategy()
        if self.user_retrieve:
            custom_boost = await UserRetrieve.get_function_score(user_id=self.user_id)
        else:
            custom_boost = None
        self.config["custom_boost"] = custom_boost

        self.chat_model_info = await ModelInfo.get_one(model_name=self.chat_model)
        self.chat_message.chat_model_id = self.chat_model_info["model_id"]
        self.config["model_id"] = self.chat_model_info["model_id"]
        self.config["model_name"] = self.chat_model_info["model_name"]

        await Session.update(session_id=self.session_id, update_time=self.chat_message.query_time)
        await g.session.commit()

    async def retrieving(self) -> List[RetrieveChunkModel]:
        self.chat_message.search_start_time = now_tz_datestring_with_millis()

        retrieval = HybridRetriever(
            request_id=self.chat_message.request_id,
            repo_search=bool(self.repo_ids),
            repo_ids=self.repo_ids,
            doc_threshold=10,
            doc_ids=self.doc_ids,
            custom_boost=self.config["custom_boost"],
            max_doc_size=self.chat_retrieval_strategy.pop("max_doc_size") or 10,
            web_search=self.web_search,
            max_input_tokens=self.chat_model_info["max_input_tokens"],
            **self.chat_retrieval_strategy)
        try:
            retrieve_chunks = await retrieval.retrieve(query=self.user_prompt, history=self.history)
        finally:
            # 修改user_prompt为改写后的结果
            if retrieval.query_rewrite:
                self.user_prompt = retrieval.query_rewrite

            # 记录信息
            self.chat_message.rewrite_user = retrieval.query_rewrite
            self.chat_message.rerank_end_time = retrieval.rerank_end_time
            self.chat_message.search_end_time = retrieval.search_end_time
            if retrieval.repo_retriever:
                self.chat_message.filter_docs = retrieval.repo_retriever.filter_docs
                self.chat_message.search_repo_chunks = retrieval.repo_retriever.search_repo_chunks
                self.chat_message.repo_search_end_time = retrieval.repo_retriever.repo_search_end_time
            if retrieval.web_retriever:
                self.chat_message.web_search_docs = retrieval.web_retriever.web_search_docs
                self.chat_message.web_extract_start_time = retrieval.web_retriever.web_extract_start_time
                self.chat_message.web_search_end_time = retrieval.web_retriever.web_search_end_time

        self.chunks = retrieve_chunks
        self.chat_message.config = self.config
        return retrieve_chunks

    async def post_processing(self):
        await Session.create_message(session_id=self.session_id, chat_message=self.chat_message)
        if self.runner and self.runner.token_consumption and g.tenant_id:
            await TokenCounts.record(
                tenant_id=g.tenant_id,
                model_name=self.chat_model,
                input_tokens=sum(self.runner.token_consumption.input_token),
                output_tokens=sum(self.runner.token_consumption.output_token),
                business=LLMBusiness.chat, create_user_id=self.user_id)
        await g.session.commit()

    def response_chunk(self, error_msg: str = None, content: str = None, reference: List[dict] = None,
                       stage: StreamStage = StreamStage.GENERATING) -> str:
        chunk = CompletionStreamResponse(
            stage=stage,
            error_msg=error_msg,
            session_id=self.session_id,
            request_id=self.chat_message.request_id,
            create_time=now_tz_datestring_with_millis(),
            content=content,
            reference=reference if reference else None,
        )
        return f"data: {chunk.model_dump_json()}\n\n"

    async def get_history(self):
        if self.history is None:
            self.history = await Session.get_chat_message_all(session_id=self.session_id, limit=MAX_HISTORY_LEN)

        # 去除历史文档的ref_id
        for hs in self.history:
            if hs.assistant:
                hs.assistant = re.sub(r"\[ref_id:\d+\]", "", hs.assistant)
        return self.history

    @staticmethod
    async def _generating(stream: AsyncIterable):
        thinking, assistant = "", ""
        async for message in stream:
            # 当前仅支持公共版 DeepSeek 的返回格式
            delta = message.choices[0].delta if message.choices and message.choices[0].delta else None

            if reasoning_content := getattr(delta, "reasoning_content", "") if delta else "":
                thinking += reasoning_content
                yield reasoning_content, StreamStage.THINKING

            if content := getattr(delta, "content", "") if delta else "":
                assistant += content
                # 处理开头的换行符，避免连续的换行符导致空内容
                if assistant.startswith("\n"):
                    assistant = assistant.replace("\n", "", 1)
                    continue
                # 为了便于后续处理，每次进入 buffer 的都只有 1 个字符
                for string in content:
                    yield string, StreamStage.GENERATING

    async def _generate_title(self):
        s = time.time()
        runner = GenerateTitle(chat_history=[self.chat_message])
        title = await runner.run()
        await Session.update(session_id=self.session_id, title=title)
        # 记录Tokens
        await TokenCounts.record(
            tenant_id=g.tenant_id, model_name=runner.model_engine.model_name, business=LLMBusiness.chat,
            input_tokens=sum(runner.token_consumption.input_token),
            output_tokens=sum(runner.token_consumption.output_token),
            create_user_id=self.user_id)
        await g.session.commit()
        logger.info(
            f"{self.session_id=} 未检测到会话标题，通过意图识别获取到的标题: {title}, 用时:{time.time() - s:.4f}")

    async def _check_stop_generation(self):
        while True:
            stop_flag = await Session.check_streaming(session_id=self.session_id)
            if not stop_flag:
                logger.info(f"{self.session_id=} 生成已停止")
                self.stop_generation = True
                break
            await asyncio.sleep(1)
