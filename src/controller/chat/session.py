#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from sqlalchemy import select, update, or_
from sqlalchemy import func

from common.time import strptime
from engine.rdb import query_order, g, fetch_one, fetch_all, paginator
from engine.cache import r_cache
from model.session import ChatSessionModel, ChatMessageModel, ChatSession, ChatMessage, ChatSessionType
from model.model_info import ModelInfoModel
from model.auth import UserModel
from exception import NotFoundError


class SessionController:
    @staticmethod
    def get_query(session_id: int = None, tenant_id: int = None, user_id: int = None, session_type: str = None,
                  match: str = None, filter_empty_message: bool = True, is_delete: bool = False, order_by: str = "update_time:desc"):
        tenant_id = tenant_id or g.tenant_id
        assert tenant_id is not None, "租户ID不可为空"

        where = [ChatSessionModel.tenant_id == tenant_id]

        if session_id is not None:
            where.append(ChatSessionModel.id == session_id)
        if user_id is not None:
            where.append(ChatSessionModel.create_user_id == user_id)
        if session_type is not None:
            where.append(ChatSessionModel.session_type == session_type)
        if match:
            where.append(ChatSessionModel.session_title.ilike(f"%{match}%"))
        if is_delete is not None:
            where.append(ChatSessionModel.is_delete == int(is_delete))

        query = (
            select(
                ChatSessionModel.id.label("session_id"),
                ChatSessionModel.session_type,
                ChatSessionModel.session_title,
                ChatSessionModel.update_time,
                func.count(ChatMessageModel.id).label("message_count"))
            .join(ChatMessageModel, ChatSessionModel.id == ChatMessageModel.session_id, isouter=not filter_empty_message)  # 过滤无消息的会话
            .where(*where)
            .group_by(ChatSessionModel.id))

        query = query_order(query=query, table=ChatSessionModel, order_by=order_by)

        return query

    async def get_one(self, session_id: int, session_type: str = None, filter_empty_message: bool = False) -> ChatSession:
        query = self.get_query(
            session_id=session_id, session_type=session_type, filter_empty_message=filter_empty_message)
        session = await fetch_one(query)
        if not session:
            raise NotFoundError("未找到指定数据")

        chat_messages = await self.get_chat_message_all(session_id=session_id)

        chat_session = ChatSession(**session, chat_history=chat_messages)
        return chat_session

    async def get_all(self, session_id: str = None, user_id: int = None, session_type: str = None, match: str = None,
                      filter_empty_message: bool = True):
        query = self.get_query(
            session_id=session_id, user_id=user_id, session_type=session_type, match=match,
            filter_empty_message=filter_empty_message)
        return await fetch_all(query)


    @staticmethod
    def get_chat_message_query(request_id: str = None, session_id: int = None, tenant_id: int = None, start: str = None,
                               end: str = None, match: str = None, order_by: str = "query_time:desc", limit: int = None,
                               need_info: bool = False):
        where = []
        columns = [
            ChatMessageModel.request_id,
            ChatMessageModel.user,
            ChatMessageModel.rewrite_user,
            ChatMessageModel.thinking,
            ChatMessageModel.assistant,
            ChatMessageModel.query_time,
            ChatMessageModel.status,
            ChatMessageModel.search_start_time,
            ChatMessageModel.search_end_time,
            ChatMessageModel.rerank_end_time,
            ChatMessageModel.streaming_start_time,
            ChatMessageModel.streaming_end_time,
            ChatMessageModel.error_msg,
            ChatMessageModel.chat_model_id,
            ChatMessageModel.repo_ids,
            ChatMessageModel.doc_ids,
            ChatSessionModel.id.label("session_id"),
            ChatSessionModel.session_title,
            ChatSessionModel.session_type,
            ModelInfoModel.show_name.label("model_show_name"),
            UserModel.nickname.label("user_nickname"),
            (func.unix_timestamp(ChatMessageModel.search_start_time) - func.unix_timestamp(ChatMessageModel.query_time)).label("indexing_duration"),
            (func.unix_timestamp(ChatMessageModel.search_end_time) - func.unix_timestamp(ChatMessageModel.search_start_time)).label("searching_duration"),
            (func.unix_timestamp(ChatMessageModel.rerank_end_time) - func.unix_timestamp(ChatMessageModel.search_end_time)).label("ranking_duration"),
            (func.unix_timestamp(ChatMessageModel.streaming_start_time) - func.unix_timestamp(ChatMessageModel.rerank_end_time)).label("first_token_duration"),
            (func.unix_timestamp(ChatMessageModel.rerank_end_time) - func.unix_timestamp(ChatMessageModel.query_time)).label("total_retrieving_duration"),
            (func.unix_timestamp(ChatMessageModel.streaming_end_time) - func.unix_timestamp(ChatMessageModel.streaming_start_time)).label("total_stream_duration"),
        ]
        if need_info:
            columns.append(ChatMessageModel.info)
        if g.tenant_id:
            where.append(ChatSessionModel.tenant_id == g.tenant_id)
        if request_id is not None:
            where.append(ChatMessageModel.request_id == request_id)
        if session_id is not None:
            where.append(ChatMessageModel.session_id == session_id)
        if match:
            where.append(
                or_(
                    ChatMessageModel.user.ilike(f"%{match}%"),
                    ChatMessageModel.rewrite_user.ilike(f"%{match}%")
                ))
        if start:
            where.append(ChatMessageModel.query_time >= start)
        if end:
            where.append(ChatMessageModel.query_time <= end)

        query = (
            select(*columns)
            .join(ChatSessionModel, ChatMessageModel.session_id == ChatSessionModel.id)
            .join(ModelInfoModel, ChatMessageModel.chat_model_id == ModelInfoModel.id, isouter=True)
            .join(UserModel, ChatMessageModel.create_user_id == UserModel.id, isouter=True)
            .where(*where)
        )
        query = query_order(query=query, table=ChatMessageModel, order_by=order_by)
        if limit is not None:
            query = query.limit(limit)

        return query

    async def get_chat_message_one(self, request_id: str):
        chat_message_query = self.get_chat_message_query(request_id=request_id, need_info=True)
        chat_message = await fetch_one(chat_message_query)
        if not chat_message:
            raise NotFoundError("未找到指定数据")

        chat_message = {**chat_message.pop("info"), **chat_message}
        if chat_message.get("repo_search_end_time") and chat_message.get("search_start_time"):
            chat_message["repo_searching_duration"] = round((strptime(chat_message["repo_search_end_time"]) - chat_message["search_start_time"]).total_seconds(), 3)
        else:
            chat_message["repo_searching_duration"] = None
        if chat_message.get("web_search_end_time") and chat_message.get("search_start_time"):
            chat_message["web_searching_duration"] = round((strptime(chat_message["web_search_end_time"]) - chat_message["search_start_time"]).total_seconds(), 3)
        else:
            chat_message["web_searching_duration"] = None

        return chat_message

    async def get_chat_message_all(self, session_id: int = None, limit: int = None):
        chat_message_query = self.get_chat_message_query(
            session_id=session_id, limit=limit, need_info=True, order_by="query_time:asc")
        chat_messages = await fetch_all(chat_message_query)

        return [ChatMessage(**{**msg.pop("info"), **msg}) for msg in chat_messages]

    async def get_chat_message_list(self, tenant_id: int = None, match: str = None, start: str = None, end: str = None,
                                    page: int = 1, per_page: int = 20, order_by: str = None):
        chat_message_query = self.get_chat_message_query(
            tenant_id=tenant_id, start=start, end=end, match=match, order_by=order_by)
        pager, chat_messages = await paginator(chat_message_query, page=page, per_page=per_page)
        return pager, chat_messages

    @staticmethod
    async def create(session_type: ChatSessionType):
        session = ChatSessionModel(session_type=session_type)
        g.session.add(session)
        await g.session.flush()

        return session.id

    @staticmethod
    async def update(session_id: int, title: str = None, update_time: str = None, session_type: ChatSessionType = None):
        update_info = {}
        if title is not None:
            update_info[ChatSessionModel.session_title] = title
        if update_time is not None:
            update_info[ChatSessionModel.update_time] = update_time
        if session_type is not None:
            update_info[ChatSessionModel.session_type] = session_type

        query = (update(ChatSessionModel)
                 .where(ChatSessionModel.id == session_id)
                 .values(update_info))
        await g.session.execute(query)

    @staticmethod
    async def delete(session_id: int, session_type: ChatSessionType = None):
        update_info = {ChatSessionModel.is_delete: True}
        if session_type is not None:
            update_info[ChatSessionModel.session_type] = session_type

        await g.session.execute(
            update(ChatSessionModel)
            .where(ChatSessionModel.id == session_id)
            .values(update_info)
        )

    # noinspection PyTypeChecker
    @staticmethod
    async def create_message(session_id: int, chat_message: ChatMessage):
        chat_message = ChatMessageModel(
            request_id=chat_message.request_id, session_id=session_id, user=chat_message.user,
            thinking=chat_message.thinking, assistant=chat_message.assistant, rewrite_user=chat_message.rewrite_user,
            query_time=chat_message.query_time, status=chat_message.qa_status, repo_ids=chat_message.repo_ids,
            doc_ids=chat_message.doc_ids, chat_model_id=chat_message.chat_model_id,
            search_start_time=chat_message.search_start_time, search_end_time=chat_message.search_end_time,
            rerank_end_time=chat_message.rerank_end_time, streaming_start_time=chat_message.streaming_start_time,
            streaming_end_time=chat_message.streaming_end_time,
            error_msg=chat_message.error_msg[:500] if chat_message.error_msg else None,
            info=chat_message.model_dump(
                exclude=("request_id", "session_id", "user", "rewrite_user", "thinking", "assistant", "qa_status",
                         "repo_ids", "doc_ids", "chat_model_id")),
        )
        g.session.add(chat_message)
        await g.session.flush()

    @staticmethod
    async def start_streaming(session_id: str):
        await r_cache.set(name=f"{session_id}_streaming", value="1", ex=60 * 60)

    @staticmethod
    async def stop_streaming(session_id: str):
        await r_cache.delete(f"{session_id}_streaming")

    @staticmethod
    async def check_streaming(session_id: str) -> bool:
        return await r_cache.exists(f"{session_id}_streaming")


Session = SessionController()