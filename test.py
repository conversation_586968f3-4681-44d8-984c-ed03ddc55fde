from engine.es import es_sync

keywords = ["钢铁"]
should = []
for kw in keywords:
    # 对text字段使用phrase查询，对keyword字段使用普通multi_match
    should.extend([
        {
            "multi_match": {
                "query": kw,
                "type": "phrase",
                "fields": ["plain_text", "filename"],
                "analyzer": "ik_max_word"
            }
        },
        {
            "multi_match": {
                "query": kw,
                "fields": ["author", "source", "tags"]
            }
        }
    ])
res = es_sync.search(
    index="repo_102",
    query={"bool": {"should": should}},
    size=10000,
    sort=[{"data_time": "asc"}],
    source_includes=["filename"]
)
print(res)