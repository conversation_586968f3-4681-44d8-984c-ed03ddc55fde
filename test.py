from engine.es import es_sync

keywords = ["山东钢铁"]
must = []

for kw in keywords:
    # 对text字段使用phrase查询，对keyword字段使用普通multi_match
    must.extend([
        {
            "match_phrase": {
                "filename": kw,
            }
        },
    ])
# 我要搜索的文档内容
# {'filename': '山东钢铁股份有限公司关于召开2024年度暨2025年第一季度业绩说明会的公告_2025-04-30.pdf',
res = es_sync.search(
    index="repo_102",
    query={"bool": {"must": must}},
    size=10000,
    sort=[{"data_time": "asc"}],
    source_includes=["filename", "plain_text"]
)
print(res)